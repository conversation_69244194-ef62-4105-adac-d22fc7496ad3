<template>
  <div class="opinion-analysis">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <div class="step-item" :class="{ active: currentStep === 1 }">
        <span class="step-number">1</span>
        <span class="step-text">舆情分析来源</span>
      </div>
      <div class="step-item" :class="{ active: currentStep === 2 }">
        <span class="step-number">2</span>
        <span class="step-text">数据概览</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 第一步：分析需求 -->
      <div v-if="currentStep === 1" class="analysis-source">
        <h2 class="section-title">分析需求</h2>

        <!-- 实体关键词区域 -->
        <div class="input-section">
          <div class="input-label">
            实体关键词
            <span class="required">*</span>
          </div>
          <el-input
            v-model="entityKeyword"
            placeholder="请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等"
            class="entity-input"
            :class="{ 'error': !entityKeyword.trim() && showValidation }"
          />
        </div>

        <!-- 具体需求区域 -->
        <div class="input-section">
          <div class="input-label">
            具体需求
            <span class="required">*</span>
          </div>
          <el-input
            v-model="specificRequirement"
            type="textarea"
            :rows="4"
            placeholder="请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）"
            class="requirement-textarea"
            :class="{ 'error': !specificRequirement.trim() && showValidation }"
          />
        </div>

        <!-- 选择关联词区域 -->
        <!--
        <div class="related-words-section">
          <div class="section-header">
            <span class="section-label">选择关联词</span>
            <span class="word-count">(0/5)</span>
          </div>

          <div class="words-container">
            <div class="generate-word-btn">
              <i class="el-icon-magic-stick"></i>
              <span>生成关联词</span>
            </div>
            <div class="word-description">
              根据你填写的需求和关键词生成关联词
            </div>
          </div>
        </div>
        -->

        <!-- 新的关键词选择区域 -->
        <div class="input-section">
          <div class="input-label">
            选择关联词
          </div>
          <div class="keywords-textbox-wrapper">
            <div class="keywords-selection-section">
              <div class="keywords-grid">
            <!-- 业绩下滑 -->
            <div class="keyword-category">
              <div class="category-label">业绩下滑</div>
              <div class="keyword-tags">
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 业绩下滑') }]"
                  @click="toggleKeyword('老板电器 业绩下滑')"
                >老板电器 业绩下滑</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 营收下降') }]"
                  @click="toggleKeyword('老板电器 营收下降')"
                >老板电器 营收下降</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 净利润下降') }]"
                  @click="toggleKeyword('老板电器 净利润下降')"
                >老板电器 净利润下降</el-tag>
              </div>
            </div>

            <!-- 质量问题 -->
            <div class="keyword-category">
              <div class="category-label">质量问题</div>
              <div class="keyword-tags">
                   <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('产品质量') }]"
                  @click="toggleKeyword('产品质量')"
                >产品质量</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 爆炸门') }]"
                  @click="toggleKeyword('老板电器 爆炸门')"
                >老板电器 爆炸门</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 投诉') }]"
                  @click="toggleKeyword('老板电器 投诉')"
                >老板电器 投诉</el-tag>
              </div>
            </div>

            <!-- 股价下跌 -->
            <div class="keyword-category">
              <div class="category-label">股价下跌</div>
              <div class="keyword-tags">
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 股价下跌') }]"
                  @click="toggleKeyword('老板电器 股价下跌')"
                >老板电器 股价下跌</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 市值缩水') }]"
                  @click="toggleKeyword('老板电器 市值缩水')"
                >老板电器 市值缩水</el-tag>
              </div>
            </div>

            <!-- 子公司亏损 -->
            <div class="keyword-category">
              <div class="category-label">子公司亏损</div>
              <div class="keyword-tags">
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 子公司亏损') }]"
                  @click="toggleKeyword('老板电器 子公司亏损')"
                >老板电器 子公司亏损</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 名气亏损') }]"
                  @click="toggleKeyword('老板电器 名气亏损')"
                >老板电器 名气亏损</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 金帝亏损') }]"
                  @click="toggleKeyword('老板电器 金帝亏损')"
                >老板电器 金帝亏损</el-tag>
              </div>
            </div>

            <!-- 渠道问题 -->
            <div class="keyword-category">
              <div class="category-label">渠道问题</div>
              <div class="keyword-tags">
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 渠道冲突') }]"
                  @click="toggleKeyword('老板电器 渠道冲突')"
                >老板电器 渠道冲突</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 串货问题') }]"
                  @click="toggleKeyword('老板电器 串货问题')"
                >老板电器 串货问题</el-tag>
                <el-tag
                  :class="['keyword-tag', { selected: isKeywordSelected('老板电器 经销商压力') }]"
                  @click="toggleKeyword('老板电器 经销商压力')"
                >老板电器 经销商压力</el-tag>
              </div>
            </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：数据概览 -->
      <div v-if="currentStep === 2" class="data-overview">
        <h2 class="section-title">选择数据来源</h2>

        <!-- 数据来源选项 -->
        <div class="data-source-section">
          <div class="source-option" @click="toggleDataSource('online-search')">
            <el-checkbox
              v-model="selectedDataSources"
              :value="'online-search'"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-search"></i>
            </div>
            <div class="source-content">
              <h3>联网搜索</h3>
            </div>
          </div>

          <!-- 自定义数据源列表 -->
          <div v-for="(source, index) in customDataSources" :key="index" class="source-option">
            <el-checkbox
              v-model="selectedDataSources"
              :value="source"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-link"></i>
            </div>
            <div class="source-content">
              <h3>{{ source }}</h3>
            </div>
            <div class="source-actions">
              <i class="el-icon-delete" @click="removeCustomSource(index)"></i>
            </div>
          </div>

          <!-- 新增数据源表单 -->
          <div v-if="showAddSourceInput" class="add-source-form">
            <div class="form-header">
              <h3>新增数据源</h3>
              <i class="el-icon-close" @click="hideAddSourceForm"></i>
            </div>
            <div class="form-item">
              <label class="form-label">
                数据源网址
                <span class="required">*</span>
              </label>
              <div class="input-group">
                <el-input
                  v-model="newSourceUrl"
                  placeholder="请输入网址，例如：https://www.example.com"
                  class="source-url-input"
                  @keyup.enter="confirmAddSource"
                />
                <el-button type="primary" @click="confirmAddSource">确定</el-button>
              </div>
            </div>
          </div>

          <!-- 新增来源按钮 -->
          <div v-if="!showAddSourceInput" class="add-source-btn" @click="showAddSourceForm">
            <i class="el-icon-plus"></i>
            <span>新增来源</span>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bottom-actions">
        <el-button v-if="currentStep === 2" @click="goToPreviousStep" size="large">上一步</el-button>
        <el-button
          v-if="currentStep === 1"
          @click="goToNextStep"
          type="primary"
          size="large"
          :disabled="!canGoToNextStep"
        >下一步</el-button>
        <el-button v-if="currentStep === 2" type="primary" size="large">开始分析</el-button>
      </div>
    </div>


  </div>
</template>

<script>
export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      currentStep: 1, // 当前步骤
      entityKeyword: '', // 实体关键词
      specificRequirement: '', // 具体需求
      selectedKeywords: [
        '老板电器 业绩下滑',
        '老板电器 营收下降',
        '老板电器 净利润下降',
        '老板电器 爆炸门'
      ], // 已选择的关键词
      maxKeywords: 5, // 最大选择数量
      selectedDataSources: ['online-search'], // 已选择的数据来源
      customDataSources: [], // 自定义数据源列表
      showAddSourceInput: false, // 显示新增数据源表单
      newSourceUrl: '', // 新增数据源URL
      showValidation: false // 是否显示验证错误样式
    }
  },
  computed: {
    // 检查是否可以进入下一步
    canGoToNextStep() {
      // 检查实体关键词是否填写
      if (!this.entityKeyword.trim()) {
        return false
      }

      // 检查具体需求是否填写
      if (!this.specificRequirement.trim()) {
        return false
      }

      // 检查是否至少选择了一个关键词
      if (this.selectedKeywords.length === 0) {
        return false
      }

      return true
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('舆情分析页面已加载')
  },
  methods: {
    // 切换关键词选择状态
    toggleKeyword(keyword) {
      const index = this.selectedKeywords.indexOf(keyword)
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedKeywords.splice(index, 1)
      } else {
        // 如果未选中，检查是否超过最大数量
        if (this.selectedKeywords.length < this.maxKeywords) {
          this.selectedKeywords.push(keyword)
        } else {
          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)
        }
      }
    },

    // 检查关键词是否已选中
    isKeywordSelected(keyword) {
      return this.selectedKeywords.includes(keyword)
    },

    // 前往下一步
    goToNextStep() {
      // 显示验证样式
      this.showValidation = true

      // 验证表单是否填写完整
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请填写具体需求')
        return
      }

      if (this.selectedKeywords.length === 0) {
        this.$message.warning('请至少选择一个关键词')
        return
      }

      // 验证通过，隐藏验证样式并进入下一步
      this.showValidation = false
      if (this.currentStep < 2) {
        this.currentStep++
      }
    },

    // 返回上一步
    goToPreviousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // 切换数据来源选择
    toggleDataSource(source) {
      const index = this.selectedDataSources.indexOf(source)
      if (index > -1) {
        this.selectedDataSources.splice(index, 1)
      } else {
        this.selectedDataSources.push(source)
      }
    },

    // 显示新增数据源表单
    showAddSourceForm() {
      this.showAddSourceInput = true
      this.newSourceUrl = ''
    },

    // 隐藏新增数据源表单
    hideAddSourceForm() {
      this.showAddSourceInput = false
      this.newSourceUrl = ''
    },

    // 确认新增数据源
    confirmAddSource() {
      if (!this.newSourceUrl.trim()) {
        this.$message.warning('请输入数据源网址')
        return
      }

      // 简单的URL格式验证
      const urlPattern = /^https?:\/\/.+/
      if (!urlPattern.test(this.newSourceUrl.trim())) {
        this.$message.warning('请输入有效的网址格式')
        return
      }

      // 检查是否已存在相同的数据源
      const trimmedUrl = this.newSourceUrl.trim()
      if (this.customDataSources.includes(trimmedUrl)) {
        this.$message.warning('该数据源已存在')
        return
      }

      // 将新的数据源添加到自定义数据源列表中
      this.customDataSources.push(trimmedUrl)
      // 自动选中新添加的数据源
      this.selectedDataSources.push(trimmedUrl)

      this.$message.success('数据源添加成功')
      // 清空输入框，但保持表单显示，允许继续添加
      this.newSourceUrl = ''
    },

    // 删除自定义数据源
    removeCustomSource(index) {
      const sourceToRemove = this.customDataSources[index]
      // 从自定义数据源列表中移除
      this.customDataSources.splice(index, 1)
      // 从已选择列表中移除
      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)
      if (selectedIndex > -1) {
        this.selectedDataSources.splice(selectedIndex, 1)
      }
      this.$message.success('数据源删除成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// 步骤指示器样式
.steps-container {
  background: white;
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
  gap: 60px;

  .step-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 14px;

    &.active {
      color: #5470c6;
      font-weight: 500;

      .step-number {
        background: #5470c6;
        color: white;
      }
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e8e8e8;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 24px;
}

// 分析来源区域
.analysis-source {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

// 输入区域样式
.input-section {
  margin-bottom: 24px;

  .input-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;

    .required {
      color: #ff4d4f;
      margin-left: 2px;
    }
  }

  .entity-input {
    :deep(.el-input__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;

      &::placeholder {
        color: #bfbfbf;
      }

      &:focus {
        border-color: #5470c6;
        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
      }
    }

    &.error {
      :deep(.el-input__inner) {
        border-color: #ff4d4f;

        &:focus {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
        }
      }
    }
  }

  .requirement-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;

      &::placeholder {
        color: #bfbfbf;
      }

      &:focus {
        border-color: #5470c6;
        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
      }
    }

    &.error {
      :deep(.el-textarea__inner) {
        border-color: #ff4d4f;

        &:focus {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
        }
      }
    }
  }
}

// 选择关联词区域
.related-words-section {
  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .section-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .word-count {
      font-size: 14px;
      color: #999;
    }
  }

  .words-container {
    text-align: center;

    .generate-word-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 8px 16px;
      background: #f0f7ff;
      color: #5470c6;
      border: 1px dashed #5470c6;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 12px;

      &:hover {
        background: #e6f4ff;
        border-color: #4096ff;
      }

      i {
        font-size: 12px;
      }
    }

    .word-description {
      font-size: 12px;
      color: #999;
      line-height: 1.5;
    }
  }
}

// 关键词文本框包装器
.keywords-textbox-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
  min-height: 120px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    border-color: #5470c6;
  }

  &:focus-within {
    border-color: #5470c6;
    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
  }
}

// 关键词选择区域
.keywords-selection-section {
  .keywords-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .keyword-category {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .category-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      min-width: 80px;
      padding-top: 6px;
    }

    .keyword-tags {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        user-select: none;

        &:hover {
          border-color: #5470c6;
          color: #5470c6;
        }

        &.selected {
          background: #5470c6;
          color: white;
          border-color: #5470c6;
        }

        &.highlight {
          background: #333;
          color: white;
          border-color: #333;
          position: relative;
          cursor: default;

          &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #333;
          }
        }
      }
    }
  }
}

// 第二步：数据概览样式
.data-overview {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

.data-source-section {
  .source-option {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #5470c6;
      background: #f8f9fa;
    }

    .source-checkbox {
      :deep(.el-checkbox__input) {
        .el-checkbox__inner {
          width: 18px;
          height: 18px;
          border-radius: 4px;
          border: 2px solid #d9d9d9;

          &::after {
            width: 5px;
            height: 9px;
            left: 5px;
            top: 1px;
          }
        }

        &.is-checked .el-checkbox__inner {
          background-color: #5470c6;
          border-color: #5470c6;
        }
      }
    }

    .source-icon {
      width: 40px;
      height: 40px;
      background: #f0f7ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 20px;
        color: #5470c6;
      }
    }

    .source-content {
      flex: 1;

      h3 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 0;
        word-break: break-all;
        line-height: 1.4;
      }
    }

    .source-actions {
      display: flex;
      align-items: center;

      .el-icon-delete {
        font-size: 16px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }

  .add-source-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #5470c6;
      color: #5470c6;
      background: #f8f9fa;
    }

    i {
      font-size: 16px;
    }
  }
}

// 底部按钮区域
.bottom-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-top: 24px;

  .el-button {
    padding: 12px 32px;
    font-size: 16px;

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
      cursor: not-allowed;
    }
  }
}

// 新增数据源表单样式
.add-source-form {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .el-icon-close {
      font-size: 18px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #666;
      }
    }
  }

  .form-item {
    .form-label {
      display: block;
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 8px;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .source-url-input {
        flex: 1;

        :deep(.el-input__inner) {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          padding: 12px 16px;
          font-size: 14px;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #5470c6;
            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
          }
        }
      }

      .el-button {
        padding: 12px 24px;
        font-size: 14px;
        border-radius: 6px;
        white-space: nowrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .steps-container {
    gap: 30px;
    padding: 16px 0;

    .step-item {
      font-size: 13px;
    }
  }

  .main-content {
    padding: 24px 16px;
  }

  .analysis-source {
    padding: 24px 20px;
  }

  .document-content {
    padding: 12px;
    min-height: 100px;
  }
}
</style>
